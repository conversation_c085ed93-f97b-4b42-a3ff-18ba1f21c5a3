import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:koc_app/src/base/base_page_state.dart';
import 'package:koc_app/src/modules/report/cubit/campaign/campaign_report_cubit.dart';
import 'package:koc_app/src/modules/report/cubit/campaign/campaign_report_state.dart';
import 'package:koc_app/src/modules/report/data/model/campaign/campaign_report_data.dart';
import 'package:koc_app/src/modules/report/data/model/report_model.dart';
import 'package:koc_app/src/modules/report/mixin/report_mixin.dart';
import 'package:koc_app/src/modules/shared/cubit/filter_cubit.dart';
import 'package:koc_app/src/modules/shared/cubit/filter_state.dart';
import 'package:koc_app/src/modules/shared/mixin/filter_mixin.dart';
import 'package:koc_app/src/shared/constants/color_constants.dart';
import 'package:koc_app/src/shared/constants/date_time_constants.dart';
import 'package:koc_app/src/shared/data/item.dart';
import 'package:koc_app/src/shared/extensions.dart';
import 'package:koc_app/src/shared/mixin/common_mixin.dart';
import 'package:koc_app/src/shared/widgets/charts/campaign_chart.dart';
import 'package:koc_app/src/shared/widgets/charts/chart_models.dart';
import 'package:koc_app/src/shared/widgets/common_app_bar.dart';
import 'package:koc_app/src/shared/widgets/pull_to_refresh_wrapper.dart';

class CampaignReportPage extends StatefulWidget {
  const CampaignReportPage({super.key});

  @override
  State<CampaignReportPage> createState() => _CampaignReportPageState();
}

class _CampaignReportPageState extends BasePageState<CampaignReportPage, CampaignReportCubit>
    with ReportMixin, CommonMixin, FilterMixin {
  @override
  void initState() {
    Modular.get<FilterCubit>().clear();
    super.initState();
  }

  @override
  Widget buildPage(BuildContext context) {
    return Scaffold(
      appBar: CommonAppBar(
        title: const Text('Campaign'),
        customAction: Row(
          children: [
            BlocBuilder<CampaignReportCubit, CampaignReportState>(builder: (_, state) {
              if (state != CampaignReportState() && state.reportData.isNotEmpty) {
                return IconButton(
                  icon: Icon(
                    Icons.file_download_outlined,
                    size: 20.r,
                  ),
                  onPressed: () {
                    saveCsvToDownloadFolder(context, convertToCsv(state), _getFileName());
                  },
                );
              }
              return const SizedBox.shrink();
            }),
            IconButton(
                onPressed: () async {
                  bool? showReport = await showFilters(
                      context,
                      [
                        ReportPeriod.THIS_MONTH,
                        ReportPeriod.LAST_MONTH,
                        ReportPeriod.LAST_3_MONTHS,
                        ReportPeriod.CUSTOM_RANGE
                      ],
                      showCampaigns: false,
                      showSites: true);
                  if (showReport != null && showReport) {
                    doLoadingAction(() async {
                      FilterState reportFilterState = Modular.get<FilterCubit>().state;
                      DateTimeRange dateTimeRange = getTimeRange(
                          reportFilterState.selectedPeriod, reportFilterState.startDate, reportFilterState.endDate);
                      await cubit.findCampaigns(
                        dateTimeRange.start,
                        dateTimeRange.end,
                        reportFilterState.selectedDateType,
                        reportFilterState.selectedStatus,
                        reportFilterState.selectedSite,
                      );
                      Modular.get<FilterCubit>().selectRangeDate(dateTimeRange.start, dateTimeRange.end);
                    });
                  }
                },
                icon: const Icon(Icons.tune)),
          ],
        ),
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    return BlocBuilder<CampaignReportCubit, CampaignReportState>(
        bloc: cubit,
        builder: (_, state) {
          if (state.showReport) {
            return PullToRefreshWrapper(
              onRefresh: _refreshData,
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                child: Column(
                  children: [
                    BlocBuilder<FilterCubit, FilterState>(
                      bloc: Modular.get<FilterCubit>(),
                      builder: (_, reportFilterState) {
                        if (state.showReport && reportFilterState != FilterState()) {
                          return buildFilterRow(context, reportFilterState,
                              showCampaigns: false,
                              showSites: true,
                              periodTemplate: [
                                ReportPeriod.THIS_MONTH,
                                ReportPeriod.LAST_MONTH,
                                ReportPeriod.LAST_3_MONTHS,
                              ]);
                        }
                        return const SizedBox.shrink();
                      },
                    ),
                    if (state.reportData.isNotEmpty) _buildCampaignResultBody(state),
                    if (state.reportData.isEmpty)
                      buildNoResultBody(
                        context,
                        () {
                          cubit.hideReport();
                          Modular.get<FilterCubit>().clear();
                        },
                      ),
                  ],
                ),
              ),
            );
          }
          return buildAddFilterMessage(context);
        });
  }

  Widget _buildCampaignResultBody(CampaignReportState state) {
    return Padding(
      padding: EdgeInsets.all(16.r),
      child: Column(
        spacing: 16.r,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildCampaignChart(state),
          _buildCampaignTable(state),
        ],
      ),
    );
  }

  Widget _buildCampaignTable(CampaignReportState state) {
    return Container(
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12.r),
          border: Border.all(
            color: const Color(0x1F000000),
            width: 1.r,
          )),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12.r),
        child: Theme(
          data: Theme.of(context).copyWith(
            cardTheme: const CardTheme(color: Colors.white),
          ),
          child: DataTable(
            headingRowColor: const WidgetStatePropertyAll(Color(0xFFF2F2F2)),
            columns: [
              buildDataColumn(context, 'Campaign'),
              buildDataColumn(context, 'Clicks'),
              buildDataColumn(context, 'Conversions'),
            ],
            rows: state.reportData.map((conversion) {
              return DataRow(
                  color: WidgetStateProperty.resolveWith<Color>((Set<WidgetState> states) {
                    return Colors.white;
                  }),
                  cells: [
                    buildDataCell(context, conversion.campaignName, color: const Color(0xFFEF6507), onTap: () {
                      Modular.to.pushNamed('/report/campaign/details',
                          arguments: Item(name: conversion.campaignName, value: conversion.campaignId));
                    }),
                    buildDataCell(context, conversion.clicks.toCommaSeparated()),
                    buildDataCell(context, conversion.conversions.toCommaSeparated()),
                  ]);
            }).toList(),
          ),
        ),
      ),
    );
  }

  CampaignChartData _getChartData(CampaignReportState state) {
    List<CampaignReportData> topCampaigns = List.from(state.reportData);
    topCampaigns.sort((a, b) => state.chartValue == CampaignChartValue.CLICKS
        ? b.clicks.compareTo(a.clicks)
        : b.conversions.compareTo(a.conversions));

    final maxItems = state.chartTitle == CampaignChartTitle.TOP_10 ? 10 : 15;
    final limitedCampaigns = topCampaigns.length <= maxItems ? topCampaigns : topCampaigns.sublist(0, maxItems);

    final chartItems = limitedCampaigns
        .map((e) => CampaignChartItem(
              name: e.campaignName,
              value: state.chartValue == CampaignChartValue.CLICKS ? e.clicks : e.conversions,
              id: e.campaignId,
            ))
        .toList()
      ..sort((a, b) => a.value.compareTo(b.value));

    return CampaignChartData(
      items: chartItems,
      valueLabel: state.chartValue.value,
      nameLabel: 'Campaign',
    );
  }

  Widget _buildCampaignChart(CampaignReportState state) {
    final chartData = _getChartData(state);

    final chartConfig = CampaignChartConfig(
      chartType: CampaignChartType.horizontalBar,
      title: '',
      showTitle: false,
      aspectRatio: 1.2,
      maxItems: state.chartTitle == CampaignChartTitle.TOP_10 ? 10 : 15,
      sortAscending: true,
      primaryColor: const Color(0xFF1AAA55),
      backgroundColor: Colors.white,
      borderColor: const Color.fromARGB(0, 255, 255, 255),
      showGrid: true,
      showTooltip: true,
      maxLabelLength: 15,
      labelTruncationSuffix: '...',
      rotateLabels: false,
      yAxisGridLines: 4,
      xAxisLabelInterval: 1,
      padding: EdgeInsets.symmetric(vertical: 8.r),
    );

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: const Color(0xFFE7E7E7),
          width: 1.r,
        ),
      ),
      child: Column(
        children: [
          Padding(
            padding: EdgeInsets.only(left: 12.r, right: 12.r, top: 12.r),
            child: Row(
              spacing: 8.r,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Container(
                    height: 40.r,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12.r),
                      color: Colors.white,
                      border: Border.all(
                        width: 1.r,
                        color: ColorConstants.borderColor,
                      ),
                    ),
                    child: DropdownButtonHideUnderline(
                      child: DropdownButton2<CampaignChartTitle>(
                        isExpanded: true,
                        value: state.chartTitle,
                        items: CampaignChartTitle.values.map((value) {
                          return DropdownMenuItem<CampaignChartTitle>(
                            value: value,
                            child: Text(
                              value.value,
                              style: context.textLabelLarge(),
                            ),
                          );
                        }).toList(),
                        onChanged: (value) {
                          if (value != null) {
                            cubit.updateChartTitle(value);
                          }
                        },
                        iconStyleData: IconStyleData(
                          icon: Icon(Icons.arrow_drop_down_outlined, size: 20.r),
                        ),
                        dropdownStyleData: DropdownStyleData(
                          maxHeight: 200.r,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(12.r),
                            color: Colors.white,
                            border: Border.all(
                              color: ColorConstants.borderColor,
                              width: 1.r,
                            ),
                          ),
                          elevation: 2,
                        ),
                        buttonStyleData: ButtonStyleData(
                          height: 40.r,
                          padding: EdgeInsets.zero,
                          decoration: BoxDecoration(
                            color: Colors.transparent,
                            borderRadius: BorderRadius.circular(12.r),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
                SizedBox(
                  width: 80.r,
                  child: Text(
                    'Campaigns sorted by',
                    style: context.textLabelMedium(),
                    textAlign: TextAlign.center,
                  ),
                ),
                Expanded(
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 12.r, vertical: 12.r),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12.r),
                      color: Colors.white,
                      border: Border.all(
                        width: 1.r,
                        color: ColorConstants.borderColor,
                      ),
                    ),
                    child: DropdownButtonHideUnderline(
                      child: DropdownButton2<CampaignChartValue>(
                        isExpanded: true,
                        value: state.chartValue,
                        items: CampaignChartValue.values.map((value) {
                          return DropdownMenuItem<CampaignChartValue>(
                            value: value,
                            child: Text(
                              value.value,
                              style: context.textLabelLarge(),
                            ),
                          );
                        }).toList(),
                        onChanged: (value) {
                          if (value != null) {
                            cubit.updateChartValue(value);
                          }
                        },
                        iconStyleData: IconStyleData(
                          icon: Icon(Icons.arrow_drop_down_outlined, size: 20.r),
                        ),
                        dropdownStyleData: DropdownStyleData(
                          maxHeight: 200.r,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(12.r),
                            color: Colors.white,
                            border: Border.all(
                              color: ColorConstants.borderColor,
                              width: 1.r,
                            ),
                          ),
                          elevation: 2,
                        ),
                        buttonStyleData: ButtonStyleData(
                          height: 40.r,
                          padding: EdgeInsets.zero,
                          decoration: BoxDecoration(
                            color: Colors.transparent,
                            borderRadius: BorderRadius.circular(12.r),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          SizedBox(
            height: 300.r,
            child: CampaignChart(
              data: chartData,
              config: chartConfig,
            ),
          ),
        ],
      ),
    );
  }

  List<List<dynamic>> convertToCsv(CampaignReportState state) {
    List<List<dynamic>> result = [
      [
        'Campaign',
        'Clicks',
        'Conversions',
      ]
    ];

    for (var report in state.reportData) {
      result.add([
        report.campaignName,
        report.clicks.toCommaSeparated(),
        report.conversions.toCommaSeparated(),
      ]);
    }
    return result;
  }

  String _getFileName() {
    FilterState reportFilterState = Modular.get<FilterCubit>().state;
    DateTimeRange dateTimeRange =
        getTimeRange(reportFilterState.selectedPeriod, reportFilterState.startDate, reportFilterState.endDate);
    DateFormat formatter = DateFormat(fullDateFormat);
    return 'CampaignReport-${formatter.format(dateTimeRange.start)}-${formatter.format(dateTimeRange.end)}';
  }

  Future<void> _refreshData() async {
    final FilterState reportFilterState = Modular.get<FilterCubit>().state;

    if (reportFilterState != FilterState() && cubit.state.showReport) {
      final DateTimeRange dateTimeRange =
          getTimeRange(reportFilterState.selectedPeriod, reportFilterState.startDate, reportFilterState.endDate);

      await cubit.pullToRefresh(dateTimeRange.start, dateTimeRange.end, reportFilterState.selectedDateType,
          reportFilterState.selectedStatus, reportFilterState.selectedSite);
    }
  }
}
